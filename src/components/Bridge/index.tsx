import { useStore } from "@/store";
import { NetworkSelector } from "@/components/NetworkSelector";
import { networks } from "@/config/network.ts";
import { Network } from "@/types/network.ts";
import Token from "@/types/token.ts";
import { observer } from "mobx-react-lite";
import { useAppKitAccount } from "@reown/appkit/react";
import { Button, Image, Input, useDisclosure } from "@heroui/react";
import TokenSelector from "@/components/TokenSelector";
import React from "react";

const Bridge = observer(() => {
  const { lang, depositStore, tokenStore } = useStore();
  const { address } = useAppKitAccount();
  const {
    isOpen: isOpenTokenSelector,
    onOpen: onOpenTokenSelector,
    onOpenChange: onOpenTokenSelectorChange,
  } = useDisclosure();

  const handleTokenSelect = React.useCallback((token: Token) => {
    depositStore.setSelectedToken(token);
  }, []);

  const handleAmountChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      // 允许空字符串
      if (value === "") {
        depositStore.setInputAmount("");
        return;
      }

      // 只允许数字和一个小数点
      const regex = /^[0-9]*\.?[0-9]*$/;
      if (!regex.test(value)) {
        return;
      }

      // 不允许多个小数点
      if ((value.match(/\./g) || []).length > 1) {
        return;
      }

      // 不允许以多个0开头（除非是0.xxx格式）
      if (value.length > 1 && value[0] === "0" && value[1] !== ".") {
        return;
      }

      // 转换为数字检查是否为负数
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue < 0) {
        return;
      }

      depositStore.setInputAmount(value);
    },
    [depositStore],
  );

  return (
    <div className="w-full">
      <div className="bg-color6 border-border rounded-xl w-full px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="font-normal text-lg text-color7 w-64">
            From Network
          </div>
          <div className="w-20" />
          <div className="font-normal text-lg text-color7 w-64">
            Destination Network
          </div>
        </div>

        <div className="w-full flex items-center justify-between mt-3">
          <NetworkSelector
            className="w-64"
            networks={networks}
            selectionChainId={depositStore.fromNetwork.chainId}
            onSelectedNetwork={function (network: Network) {
              depositStore.setFromNetwork(network);
            }}
          >
            <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64">
              <div className="flex items-center">
                <Image
                  className="size-6"
                  src={depositStore.fromNetwork.logoUrl}
                />
                <div className="text-lg text-color8 ml-2">
                  {depositStore.fromNetwork.name}
                </div>
              </div>
              <Image className="size-6" src="/public/images/icon_down.svg" />
            </Button>
          </NetworkSelector>
          <Image
            className="size-12 mx-4 cursor-pointer"
            src="/public/images/icon_arrow.svg"
            onClick={() => depositStore.swapNetworks()}
          />
          <NetworkSelector
            networks={depositStore.fromNetwork.destNetworks}
            selectionChainId={depositStore.destNetwork.chainId}
            onSelectedNetwork={function (network: Network) {
              depositStore.setDestNetwork(network);
            }}
          >
            <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64">
              <div className="flex items-center">
                <Image
                  className="size-6 rounded-full"
                  src={depositStore.destNetwork.logoUrl}
                />
                <div className="text-lg text-color8 ml-2">
                  {depositStore.destNetwork.name}
                </div>
              </div>
              <Image className="size-6" src="/public/images/icon_down.svg" />
            </Button>
          </NetworkSelector>
        </div>

        <div className="flex items-center justify-between mt-6">
          <div className="text-lg text-color7">Select Token</div>
          <div className="text-sm text-color8">
            Balance: {depositStore.selectedToken?.balance?.formattedAmount}{" "}
            {depositStore.selectedToken?.symbol}
          </div>
        </div>

        <div className="w-full flex items-center justify-between mt-3">
          <Button
            className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 w-64"
            onPress={onOpenTokenSelector}
          >
            <div className="flex items-center">
              <Image
                className="size-6"
                src={depositStore.selectedToken?.logouri}
              />
              <div className="text-lg text-color8 ml-2">
                {depositStore.selectedToken?.symbol}
              </div>
            </div>
            <Image className="size-6" src="/public/images/icon_down.svg" />
          </Button>
          <div className="w-6" />
          <div className="px-4 border-1 border-color9 box-border rounded-full flex items-center justify-between w-64 h-12">
            <Input
              className="mr-2 flex-1"
              classNames={{
                input: [
                  "bg-transparent",
                  "text-color8",
                  "placeholder:text-color7 dark:placeholder:text-white/60",
                  "text-lg",
                ],
                innerWrapper: "bg-transparent",
                inputWrapper: [
                  "bg-transparent",
                  "dark:transparent",
                  "hover:bg-transparent",
                  "dark:hover:bg-transparent",
                  "group-data-[focus=true]:bg-transparent",
                  "!cursor-text",
                ],
              }}
              placeholder="0"
              type="text"
              value={depositStore.inputAmount}
              onChange={handleAmountChange}
            />
            <Button className="text-purple1 text-lg p-0 bg-transparent min-w-0">
              MAX
            </Button>
          </div>
        </div>

        <div className="mt-6 text-lg text-color7">Recipient Address</div>
        <div className="mt-3 border-1 border-color9 rounded-full h-12 flex items-center justify-between p-4 gap-2">
          <Input
            className="mr-2 flex-1"
            classNames={{
              input: [
                "bg-transparent",
                "text-color8",
                "placeholder:text-color7 dark:placeholder:text-white/60",
                "text-lg",
              ],
              innerWrapper: "bg-transparent",
              inputWrapper: [
                "bg-transparent",
                "dark:transparent",
                "hover:bg-transparent",
                "dark:hover:bg-transparent",
                "group-data-[focus=true]:bg-transparent",
                "!cursor-text",
              ],
            }}
            placeholder="0x"
            value={depositStore.receiptAddress}
            onChange={(e) => {
              depositStore.setReceiptAddress(e.target.value);
            }}
          />
          <Button
            className="text-purple1 text-lg p-0 bg-transparent"
            onPress={() => {
              depositStore.setReceiptAddress(address);
            }}
          >
            My Wallet
          </Button>
        </div>

        <div className="mt-6 text-lg text-color7">
          Get on {depositStore.destNetwork.name}
        </div>
        <div className="mt-2 flex items-center">
          <Image
            className="size-10 rounded-full"
            src={depositStore.selectedToken?.logouri}
          />
          <div className="ml-3">
            <div className="text-color8 text-2xl">
              99.99 {depositStore.selectedToken?.symbol}
            </div>
            <div className="text-color7 text-xs">$2.156</div>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_coin.svg" />
            <div className="text-xs text-color7 ml-2">
              Relay Fee: 0.00045ETH
            </div>
            <Image
              className="size-4 ml-4"
              src="/public/images/icon_exchange.svg"
            />
            <div className="text-xs text-color7 ml-2">Tube fee: 0</div>
          </div>
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_hourglass.svg" />
            <div className="text-xs text-color7 ml-2">About 5 mins</div>
          </div>
        </div>
      </div>

      <Button className="w-full rounded-full mt-8 h-12 font-bold text-xl bg-gradient-to-b from-purple3 to-purple4">
        Deposit
      </Button>

      <div className="text-color7 text-xs mt-4 text-center">
        {lang.t("description")}
      </div>

      <TokenSelector
        isOpen={isOpenTokenSelector}
        tokens={tokenStore.tokenList}
        onOpenChange={onOpenTokenSelectorChange}
        onTokenSelect={handleTokenSelect}
      />
    </div>
  );
});

export default Bridge;
